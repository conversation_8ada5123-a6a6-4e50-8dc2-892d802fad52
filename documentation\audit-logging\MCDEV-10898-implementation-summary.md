# MCDEV-10898: Events Registration Tab Audit Logging Implementation

## Overview
This story implements comprehensive audit logging for three key methods in the Events Registration tab:
1. `saveEventRegDetails()` - Registration configuration updates
2. `saveAltURL()` - Alternate URL configuration  
3. `deleteRegistrationSchedule()` - Registration schedule deletion

## Implementation Details

### 1. saveEventRegDetails() Method
**Location**: `membercentral/model/admin/events/EventAdmin.cfc` (lines 3596-3799)
**Approach**: Temp table pattern for comprehensive change tracking
**Area Code**: 'REGISTRATION'

**Key Features**:
- Uses `##tmpRegAuditLogData` temp table to track field changes
- Captures old values before updates, new values after updates
- Handles both registration creation and update scenarios
- Tracks 15 key registration fields including dates, capacity, online meeting settings
- Uses `ams_getAuditLogMsg` for intelligent change detection
- Only logs audit entry if actual changes are detected

**Fields Tracked**:
- Registration Type (Full Registration vs RSVP)
- Start Date, End Date, Registrant Cap
- Reply To Email, Notify Email
- Online Meeting settings (enabled, embed code, override link, times)
- Real Time Roster, Edit Allowed, Edit Deadline

**evKeyMapJSON Format**: `{ "REGISTRATIONID":123, "EVENTID":456 }`

### 2. saveAltURL() Method
**Location**: `membercentral/model/admin/events/EventAdmin.cfc` (lines 3882-3957)
**Approach**: Simple message logging
**Area Code**: 'REGISTRATION'

**Key Features**:
- Captures old and new alternate URLs for comparison
- Uses ColdFusion Query object for audit logging
- Includes error handling to prevent audit failures from affecting main operation
- Logs meaningful message showing URL transition

**Message Format**: "Alternate registration URL updated for event [EventTitle] from [OldURL] to [NewURL]."

**evKeyMapJSON Format**: `{ "REGISTRATIONID":123, "EVENTID":456 }`

### 3. deleteRegistrationSchedule() Method
**Location**: `membercentral/model/admin/events/event.cfc` (lines 3658-3716)
**Approach**: Simple message logging with schedule details
**Area Code**: 'REGISTRATION'

**Key Features**:
- Captures schedule details before deletion via complex JOIN query
- Gets event context (title, eventID) and registration context (registrationID)
- Logs schedule name and date range for audit trail
- Integrates seamlessly with existing deletion logic

**Message Format**: "Registration schedule [ScheduleName] (StartDate to EndDate) deleted for event [EventTitle]."

**evKeyMapJSON Format**: `{ "REGISTRATIONID":123, "EVENTID":456 }`

## Common Implementation Patterns

### evKeyMapJSON Structure
All three methods use the specified format:
```json
{ "REGISTRATIONID":123, "EVENTID":456 }
```

### Area Code Consistency
All methods use `'REGISTRATION'` as the area code for consistent categorization.

### Error Handling
- SQL-based implementations use standard TRY/CATCH with `up_MCErrorHandler`
- ColdFusion-based implementation uses try/catch to prevent audit failures from affecting main operations
- All follow established MemberCentral error handling patterns

### Message Formatting
- Uses `STRING_ESCAPE(..., 'json')` for proper JSON message formatting
- Includes event titles and relevant context for meaningful audit entries
- Handles null values gracefully with `ISNULL()` functions

## Integration with Existing Infrastructure

### Dependencies
- `ev_insertAuditLog` stored procedure (MCDEV-10869)
- `auditLog_EV.cfc` MongoDB model (MCDEV-10869)
- `ams_getAuditLogMsg` stored procedure for change detection
- Queue-based processing via `platformQueue.dbo.queue_mongo`

### MongoDB Collection
All audit entries are stored in the `auditLog_EV` collection in the `membercentralAudit` database with the following structure:
- `AUDITCODE`: "EV"
- `AREACODE`: "REGISTRATION"
- `ORGID`: Organization ID
- `SITEID`: Site ID
- `ACTORMEMBERID`: Member performing the action
- `ACTIONDATE`: Timestamp of action
- `MESSAGE`: Formatted audit message
- `EVKEYMAP`: JSON object with REGISTRATIONID and EVENTID

## Testing Recommendations

### 1. Registration Details Testing
- Create new registration (Full Registration and RSVP)
- Update existing registration configuration
- Modify online meeting settings
- Change capacity limits and email settings
- Verify temp table cleanup and change detection

### 2. Alternate URL Testing
- Set alternate URL on event without existing registration
- Change alternate URL on event with existing registration
- Remove alternate URL (set to empty)
- Verify old/new URL comparison in audit messages

### 3. Schedule Deletion Testing
- Delete registration schedule with various date ranges
- Verify schedule details are captured before deletion
- Test with different event titles and schedule names
- Confirm proper eventID and registrationID context

### 4. MongoDB Verification
Query the audit collection to verify entries:
```javascript
db.AuditLog_EV.find({
  "AREACODE": "REGISTRATION",
  "EVKEYMAP.EVENTID": 12345
}).sort({"ACTIONDATE": -1})
```

## Files Modified
1. `membercentral/model/admin/events/EventAdmin.cfc` - Added audit logging to saveEventRegDetails() and saveAltURL()
2. `membercentral/model/admin/events/event.cfc` - Added audit logging to deleteRegistrationSchedule()
3. `documentation/audit-logging/MCDEV-10898-implementation-summary.md` - This documentation

## Compliance with Existing Patterns
This implementation follows all established patterns from previous audit logging stories:
- MCDEV-10869: Core infrastructure usage
- MCDEV-10764: eventKeyMapJSON pattern (adapted for registrationID/eventID)
- MCDEV-10887: Temp table pattern for change tracking
- MCDEV-10885: Registration area code usage

The implementation ensures consistency with the broader MemberCentral audit logging ecosystem while providing comprehensive coverage of Events Registration tab operations.
