USE membercentral
GO

ALTER PROCEDURE dbo.ev_createRegistration
@eventID int,
@registrationTypeID int,
@startDate datetime,
@endDate datetime,
@registrantCap int,
@replyToEmail varchar(200),
@notifyEmail varchar(200),
@isPriceBasedOnActual bit,
@bulkCountByRate bit,
@registrationID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	BEGIN TRANSACTION;

	declare @appCreatedContentResourceTypeID int, @defaultLanguageID int, @enableRealTimeRoster bit,
			@contentSiteResourceID int, @siteid int, @expirationcontentid int, @paymentContentId int, @registrantCapContentID int,
			@registrantEditDeadlineContentID int, @registrantEditRefundContentID int;

	-- Audit logging variables
	DECLARE @orgID int, @calendarID int, @eventTitle varchar(200), @registrationTypeName varchar(100), @msgjson varchar(max);

	set @registrationID = null;
	select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent');

	-- Get site info and audit data
	select @siteID=s.siteID, @defaultLanguageID=s.defaultLanguageID, @orgID=s.orgID
	from dbo.sites as s
	inner join dbo.ev_events as e on e.siteID = s.siteID
	where e.eventid = @eventID;

	-- Get calendar info for eventKeyMapJSON pattern
	SELECT @calendarID = ce.calendarID
	FROM dbo.ev_events e
	INNER JOIN dbo.ev_calendarEvents ce ON ce.sourceEventID = e.eventID AND ce.calendarID = ce.sourceCalendarID
	WHERE e.eventID = @eventID;

	-- Get event title for audit message	
	SELECT TOP 1 @eventTitle = eventcontent.contentTitle
	FROM dbo.ev_events as e 
	INNER JOIN dbo.cms_contentLanguages AS eventContent ON eventContent.contentID = e.eventContentID 
		AND eventContent.languageID = 1
	WHERE e.eventID = @eventID;

	-- Get registration type name for audit message
	SELECT @registrationTypeName = registrationType
	FROM dbo.ev_registrationTypes
	WHERE registrationTypeID = @registrationTypeID;

	select top 1 @enableRealTimeRoster = c.enableRealTimeRoster
	from dbo.ev_events as ev
	INNER JOIN dbo.ev_calendarEvents as ce on ce.sourceEventID = ev.eventID and ce.calendarID = ce.sourceCalendarID
	INNER JOIN dbo.ev_calendars as c on c.siteID = @siteID and c.calendarID = ce.calendarID
	where ev.siteID = @siteID
	and ev.eventID = @eventID;

	SELECT @registrationID = registrationID
	FROM dbo.ev_registration WITH (UPDLOCK, HOLDLOCK)
	WHERE eventID = @eventID
	AND [status] <> 'D';

	IF @registrationID IS NULL BEGIN
		-- add entry without content objects to get the entry in immediately
		insert into dbo.ev_registration (eventID, siteID, registrationTypeID, startDate, endDate, registrantCap,  
			replyToEmail, notifyEmail, [status], isPriceBasedOnActual, bulkCountByRate, enableRealTimeRoster)
		values (@eventID, @siteID, @registrationTypeID, @startDate, @endDate, @registrantCap,  
			@replyToEmail, @notifyEmail, 'A', @isPriceBasedOnActual, @bulkCountByRate, @enableRealTimeRoster);

		select @registrationID = SCOPE_IDENTITY()

		-- create default reg schedule entry
		IF @registrationTypeID = 1 
			insert into dbo.ev_priceSchedule (registrationID, rangeName, startDate, endDate)
			values (@registrationID, 'Regular Registration', @startDate, @endDate);

		-- now create the content objects and update registration
		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
			@siteResourceStatusID=1, @isHTML=1, @languageID=@defaultLanguageID, 
			@isActive=1, @contentTitle='Expiration Message', @contentDesc=null, @rawContent='', @memberID=NULL, 
			@contentID=@expirationcontentid OUTPUT, @siteResourceID=@contentSiteResourceID OUTPUT;

		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
			@siteResourceStatusID=1, @isHTML=1, @languageID=@defaultLanguageID, 
			@isActive=1, @contentTitle='Registration Cap Message', @contentDesc=null, @rawContent='', @memberID=NULL,
			@contentID=@registrantCapContentID OUTPUT, @siteResourceID=@contentSiteResourceID OUTPUT;

		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
			@siteResourceStatusID=1, @isHTML=1, @languageID=@defaultLanguageID, 
			@isActive=1, @contentTitle='Editing Expiration Message', @contentDesc=null, @rawContent='', @memberID=NULL, 
			@contentID=@registrantEditDeadlineContentID OUTPUT, @siteResourceID=@contentSiteResourceID OUTPUT;

		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
			@siteResourceStatusID=1, @isHTML=1, @languageID=@defaultLanguageID, 
			@isActive=1, @contentTitle='Editing Refund Policy', @contentDesc=null, @rawContent='', @memberID=NULL, 
			@contentID=@registrantEditRefundContentID OUTPUT, @siteResourceID=@contentSiteResourceID OUTPUT;

		UPDATE dbo.ev_registration
		SET expirationContentID = @expirationcontentid,
			registrantCapContentID = @registrantCapContentID,
			registrantEditDeadlineContentID = @registrantEditDeadlineContentID,
			registrantEditRefundContentID = @registrantEditRefundContentID
		WHERE registrationID = @registrationID;

		-- Audit logging for registration creation
		IF @calendarID IS NOT NULL AND @orgID IS NOT NULL BEGIN
			DECLARE @evKeyMapJSON varchar(100) = '{ "EVENTID":'+CAST(@eventID AS varchar(10))+', "CALENDARID":'+CAST(@calendarID AS VARCHAR(10))+', "REGISTRATIONID":'+CAST(@registrationID AS VARCHAR(10))+' }';
			SET @msgjson = STRING_ESCAPE('Registration setup created for event [' + @eventTitle + '] with type [' + ISNULL(@registrationTypeName, 'Unknown') + '] from [' + CONVERT(varchar(20), @startDate, 120) + '] to [' + CONVERT(varchar(20), @endDate, 120) + '].', 'json');

			-- Note: Using @enteredByMemberID=0 since this procedure doesn't have that parameter
			-- In practice, this will be called from contexts where the member ID is known
			EXEC dbo.ev_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='REGISTRATION', @msgjson=@msgjson, @evKeyMapJSON=@evKeyMapJSON, @isImport=0, @enteredByMemberID=0;
		END
	END

	COMMIT TRANSACTION;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ev_queueCreatingRecurringEvents 
@siteID int,
@createdFromEventID int,
@afID int,
@endDate datetime,
@recordedByMemberID int,
@recurringEventsImportResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#mc_EvImport') IS NOT NULL
		DROP TABLE #mc_EvImport;
	IF OBJECT_ID('tempdb..#tmpRecurringEvents') IS NOT NULL
		DROP TABLE #tmpRecurringEvents;
	IF OBJECT_ID('tempdb..#tmp_CF_ItemIDs') IS NOT NULL
		DROP TABLE #tmp_CF_ItemIDs;
	IF OBJECT_ID('tempdb..#tmp_CF_FieldData') IS NOT NULL
		DROP TABLE #tmp_CF_FieldData;
	IF OBJECT_ID('tempdb..#tmpSWCF') IS NOT NULL
		DROP TABLE #tmpSWCF;
	
	-- bit cols defined as varchar for import validation
	CREATE TABLE #mc_EvImport (rowID int, Calendar varchar(100), EventTitle varchar(200), EventSubTitle varchar(200), InternalNotes varchar(max), EventCode varchar(15) NOT NULL, EventCategory varchar(max), 
		EventStart datetime, EventEnd datetime, EventHidden varchar(10), EventAllDay varchar(10), EventDescription varchar(max), ContactTitle varchar(200), 
		Contact varchar(max), ContactInclude varchar(max), LocationTitle varchar(200), Location varchar(max), LocationInclude varchar(max), 
		CancellationTitle varchar(200), Cancellation varchar(max), CancellationInclude varchar(max), TravelTitle varchar(200), Travel varchar(max), TravelInclude varchar(max), 
		InformationTitle varchar(200), Information varchar(max), RecurringSeriesCode varchar(15), RegistrationReplyEmail varchar(400));
	CREATE TABLE #tmpRecurringEvents (rowID int PRIMARY KEY IDENTITY(1,1), createdFromEventID int, eventCode varchar(15), startDate datetime, endDate datetime);
	CREATE TABLE #tmp_CF_ItemIDs (itemID int, itemType varchar(20));
	CREATE TABLE #tmp_CF_FieldData (fieldID int, fieldValue varchar(max), amount decimal(14,2), itemID int);

	-- Audit logging variables
	DECLARE @orgID int, @calendarID int, @eventTitle varchar(200), @formulaDesc varchar(200), @msgjson varchar(max);

	DECLARE @seriesID int, @eventStart datetime, @eventEnd datetime, @swcfList varchar(max), 
		@fullsql varchar(max), @tmpSuffix varchar(10), @datePart varchar(20), @dateNum int, @adjustTerm varchar(12), 
		@nextWeekday int, @weekNumber varchar(4), @eventStartDate datetime, @eventEndDate datetime, @minRowID int,
		@eventCode varchar(15), @calendarPageName varchar(50), @categoryIDList varchar(200), @categoryList varchar(max);

	SET @tmpSuffix = CAST(@createdFromEventID AS varchar(10));

	-- Get audit data for recurring events logging
	SELECT @orgID = s.orgID, @calendarID = ce.calendarID, @eventTitle = eventcontent.contentTitle
	FROM dbo.sites s
	INNER JOIN dbo.ev_events e ON e.siteID = s.siteID
	INNER JOIN dbo.ev_calendarEvents ce ON ce.sourceEventID = e.eventID AND ce.calendarID = ce.sourceCalendarID
	INNER JOIN dbo.cms_contentLanguages AS eventContent ON eventContent.contentID = e.eventContentID 
		AND eventContent.languageID = 1
	WHERE e.eventID = @createdFromEventID;

	-- Get advance formula description for audit message
	SELECT @formulaDesc = afName FROM dbo.af_advanceFormulas WHERE afID = @afID;

	SELECT @seriesID = seriesID
	FROM dbo.ev_recurringSeries
	WHERE createdFromEventID = @createdFromEventID
	AND siteID = @siteID;

	IF @seriesID IS NOT NULL
		GOTO on_done;

	SET @endDate = DATEADD(MS,-3,DATEADD(DAY,1,@endDate));

	SELECT @eventStartDate = et.startTime, @eventEndDate = et.endTime, @calendarPageName = aip.pageName,
		@categoryIDList = cat.categoryIDList
	FROM dbo.ev_events AS e
	INNER JOIN dbo.ev_times AS et ON et.eventid = e.eventID
		AND et.timeID = e.defaultTimeID
	INNER JOIN dbo.ev_calendarEvents AS ce ON ce.sourceEventID = e.eventID 
		AND ce.calendarID = ce.sourceCalendarID
	INNER JOIN dbo.ev_calendars AS c ON c.siteID = @siteID
		AND c.calendarID = ce.calendarID
	INNER JOIN dbo.cms_applicationInstances AS ai ON ai.siteID = @siteID
		AND ai.applicationInstanceID = c.applicationInstanceID
	CROSS APPLY dbo.fn_cms_getApplicationInstancePagePath(@siteID,c.applicationInstanceID) as aip
	LEFT OUTER JOIN dbo.cache_calendarEventsCategoryIDList AS cat ON cat.eventID = e.eventID 
		AND cat.calendarID = ce.calendarID
	WHERE e.eventID = @createdFromEventID
	AND e.siteID = @siteID
	AND aip.applicationSiteResourceID = ai.siteResourceID
	AND aip.applicationSiteResourceType = 'Events';

	IF CAST(@endDate AS DATE) > CAST(DATEADD(YEAR,2,@eventStartDate) AS DATE)
		RAISERROR('Invalid Recurring End Date.', 16, 1);

	SELECT @datePart = [datePart], @dateNum = dateNum, @adjustTerm = adjustTerm, @nextWeekday = nextWeekday, @weekNumber = weekNumber
	FROM dbo.af_advanceFormulas
	WHERE AFID = @afID
	AND siteID = @siteID;

	WITH evDates AS (
		SELECT dbo.fn_af_getAFDate(@eventStartDate,@datePart,@dateNum,@adjustTerm,@nextWeekday,@weekNumber) AS startDate, 
			dbo.fn_af_getAFDate(@eventEndDate,@datePart,@dateNum,@adjustTerm,@nextWeekday,@weekNumber) AS endDate,
			1 AS rowNum
		UNION ALL
		SELECT startDate, endDate, rowNum
		FROM (
			SELECT dbo.fn_af_getAFDate(@eventStartDate,@datePart,@dateNum * (rowNum + 1),@adjustTerm,@nextWeekday,@weekNumber) AS startDate, 
				dbo.fn_af_getAFDate(@eventEndDate,@datePart,@dateNum * (rowNum + 1),@adjustTerm,@nextWeekday,@weekNumber) AS endDate,
				rowNum + 1 AS rowNum
			FROM evDates
		) tmp
		WHERE startDate <= @endDate
	)
	INSERT INTO #tmpRecurringEvents (createdFromEventID, startDate, endDate)
	SELECT @createdFromEventID, startDate, endDate
	FROM evDates
	OPTION (MAXRECURSION 300);

	SELECT @minRowID = MIN(rowID) FROM #tmpRecurringEvents;
	WHILE @minRowID IS NOT NULL BEGIN
		SET @eventCode = NULL;

		EXEC dbo.getUniqueCode @uniqueCode=@eventCode OUTPUT;

		UPDATE #tmpRecurringEvents
		SET eventCode = @eventCode
		WHERE rowID = @minRowID;

		SELECT @minRowID = MIN(rowID) FROM #tmpRecurringEvents WHERE rowID > @minRowID;
	END

	SELECT @categoryList = STRING_AGG(evCat.category,'|')
	FROM dbo.fn_IntListToTable(@categoryIDList,',') as tmpCat
	INNER JOIN dbo.ev_categories as evCat on evCat.categoryID = tmpCat.listItem;

	SET @categoryList = ISNULL(@categoryList,'');

	-- event custom fields	
	IF OBJECT_ID('tempdb..##tmpSWCF'+@tmpSuffix) IS NOT NULL
		EXEC ('DROP TABLE ##tmpSWCF'+@tmpSuffix);
	
	INSERT INTO #tmp_CF_ItemIDs (itemID, itemType)
	SELECT siteResourceID, 'CrossEvent'
	FROM dbo.ev_events
	WHERE eventID = @createdFromEventID
	AND siteID = @siteID;

	EXEC dbo.cf_getFieldData;

	SELECT e.eventID, replace(f.fieldReference,',','') as fieldReference, 
		CASE WHEN ft.displayTypeCode IN ('SELECT','RADIO','CHECKBOX') THEN REPLACE(fd.fieldValue,', ', '|') ELSE fd.fieldValue END AS answer
	INTO #tmpSWCF
	FROM #tmp_CF_FieldData AS fd
	INNER JOIN dbo.cf_fields AS f ON f.fieldID = fd.fieldID
	INNER JOIN dbo.cf_fieldTypes AS ft ON ft.fieldTypeID = f.fieldTypeID
	INNER JOIN dbo.ev_events as e ON e.siteID = @siteID and e.siteResourceID = fd.itemID;

	-- event custom fields pivoted
	set @swcfList = '';
	select @swcfList = COALESCE(@swcfList + ',', '') + quoteName(fieldReference) from #tmpSWCF group by fieldReference;
	IF left(@swcfList,1) = ','
		set @swcfList = right(@swcfList,len(@swcfList)-1);
	IF len(@swcfList) > 0 BEGIN
		-- add swcf cols to import table
		select @fullsql = COALESCE(@fullsql, '') + 'ALTER TABLE #mc_EvImport ADD ' + quoteName(fieldReference)  + ' varchar(max);'
		from #tmpSWCF 
		group by fieldReference;

		EXEC(@fullsql);

		set @fullsql = '
			select * 
			into ##tmpSWCF'+@tmpSuffix+'
			from (
				select eventID, fieldReference, answer
				from #tmpSWCF
			) as cf
			PIVOT (min(answer) for fieldReference in (' + @swcfList + ')) as p ';
		EXEC(@fullsql);
	END
	ELSE
		EXEC('SELECT eventID INTO ##tmpSWCF'+@tmpSuffix+' FROM #tmpSWCF WHERE 0=1');

	SET @fullsql = 'SELECT ROW_NUMBER() OVER (ORDER BY tmp.startDate) AS rowID, '''+@calendarPageName+''', eventcontent.contentTitle, ev.eventSubTitle, ev.internalNotes, tmp.eventCode, '''+@categoryList+''', tmp.startDate, tmp.endDate, 
		ISNULL(ev.hiddenFromCalendar,0), ev.isAllDayEvent, eventcontent.rawContent, contactcontent.contentTitle, contactcontent.rawContent, ISNULL(ev.emailContactContent,0),
		locationcontent.contentTitle, locationcontent.rawContent, ISNULL(ev.emailLocationContent,0), cancelcontent.contentTitle, cancelcontent.rawContent, 
		ISNULL(ev.emailCancelContent,0), travelcontent.contentTitle, travelcontent.rawContent, ISNULL(ev.emailTravelContent,0), 
		informationcontent.contentTitle, informationcontent.rawContent, ev.reportCode, '''' AS RegistrationReplyEmail';
	IF len(@swcfList) > 0
		SET @fullsql = @fullsql + ', swcf.' + replace(@swcfList,',',',swcf.');
	SET @fullsql = @fullsql + '
		FROM #tmpRecurringEvents AS tmp
		INNER JOIN dbo.ev_events AS ev ON ev.siteID = '+CAST(@siteID AS varchar(10))+' AND ev.eventID = tmp.createdFromEventID
		CROSS APPLY dbo.fn_getContent(ev.eventcontentID,1) AS eventcontent
		CROSS APPLY dbo.fn_getContent(ev.locationcontentID,1) AS locationcontent
		CROSS APPLY dbo.fn_getContent(ev.travelcontentID,1) AS travelcontent
		CROSS APPLY dbo.fn_getContent(ev.contactcontentID,1) AS contactcontent
		CROSS APPLY dbo.fn_getContent(ev.cancellationPolicycontentID,1) AS cancelcontent
		CROSS APPLY dbo.fn_getContent(ev.informationContentID,1) AS informationcontent';
	IF len(@swcfList) > 0
		SET @fullsql = @fullsql + '
			LEFT OUTER JOIN ##tmpSWCF'+@tmpSuffix+' AS swcf ON swcf.eventID = ev.eventID';

	
	INSERT INTO #mc_EvImport
	EXEC(@fullsql);

	-- drop global table
	IF OBJECT_ID('tempdb..##tmpSWCF'+@tmpSuffix) IS NOT NULL
		EXEC ('DROP TABLE ##tmpSWCF'+@tmpSuffix);

	BEGIN TRAN;
		INSERT INTO dbo.ev_recurringSeries (siteID, createdFromEventID, afID)
		VALUES (@siteID, @createdFromEventID, @afID);

		SET @seriesID = SCOPE_IDENTITY();

		UPDATE dbo.ev_events
		SET recurringSeriesID = @seriesID
		WHERE eventID = @createdFromEventID;

		-- Audit logging for recurring series creation
		IF @calendarID IS NOT NULL AND @orgID IS NOT NULL BEGIN
			DECLARE @eventCount int, @evKeyMapJSON varchar(100);
			SELECT @eventCount = COUNT(*) FROM #tmpRecurringEvents;

			SET @evKeyMapJSON = '{ "EVENTID":'+CAST(@createdFromEventID AS varchar(10))+', "CALENDARID":'+CAST(@calendarID AS VARCHAR(10))+' }';
			SET @msgjson = STRING_ESCAPE('Recurring event series created for event [' + @eventTitle + '] with pattern [' + ISNULL(@formulaDesc, 'Custom') + '] ending [' + CONVERT(varchar(20), @endDate, 120) + '], creating [' + CAST(@eventCount AS varchar(10)) + '] events queued for import.', 'json');

			EXEC dbo.ev_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='EVENT', @msgjson=@msgjson, @evKeyMapJSON=@evKeyMapJSON, @isImport=0, @enteredByMemberID=@recordedByMemberID;
		END
	COMMIT TRAN;

	-- queue recurring events import
	EXEC dbo.ev_importEvents @siteID=@siteID, @recordedByMemberID=@recordedByMemberID, @ovAction='s', @importResult=@recurringEventsImportResult OUTPUT;

	on_done:

	IF OBJECT_ID('tempdb..#mc_EvImport') IS NOT NULL
		DROP TABLE #mc_EvImport;
	IF OBJECT_ID('tempdb..#tmpRecurringEvents') IS NOT NULL
		DROP TABLE #tmpRecurringEvents;
	IF OBJECT_ID('tempdb..#tmp_CF_ItemIDs') IS NOT NULL
		DROP TABLE #tmp_CF_ItemIDs;
	IF OBJECT_ID('tempdb..#tmp_CF_FieldData') IS NOT NULL
		DROP TABLE #tmp_CF_FieldData;
	IF OBJECT_ID('tempdb..#tmpSWCF') IS NOT NULL
		DROP TABLE #tmpSWCF;
	IF OBJECT_ID('tempdb..##tmpSWCF'+@tmpSuffix) IS NOT NULL
		EXEC ('DROP TABLE ##tmpSWCF'+@tmpSuffix);

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROCEDURE dbo.ev_updateUpcomingRecurringEvents 
@siteID int,
@copyFromEventID int,
@recordedByMemberID int,
@recurringEventsImportResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#mc_EvImport') IS NOT NULL
		DROP TABLE #mc_EvImport;
	IF OBJECT_ID('tempdb..#tmpExistingRecurringEvents') IS NOT NULL
		DROP TABLE #tmpExistingRecurringEvents;
	IF OBJECT_ID('tempdb..#tmp_CF_ItemIDs') IS NOT NULL
		DROP TABLE #tmp_CF_ItemIDs;
	IF OBJECT_ID('tempdb..#tmp_CF_FieldData') IS NOT NULL
		DROP TABLE #tmp_CF_FieldData;
	IF OBJECT_ID('tempdb..#tmpSWCF') IS NOT NULL
		DROP TABLE #tmpSWCF;
	
	-- bit cols defined as varchar for import validation
	CREATE TABLE #mc_EvImport (rowID int, Calendar varchar(100), EventTitle varchar(200), EventSubTitle varchar(200), InternalNotes varchar(max), EventCode varchar(15) NOT NULL, EventCategory varchar(max), 
		EventStart datetime, EventEnd datetime, EventHidden varchar(10), EventAllDay varchar(10), EventDescription varchar(max), ContactTitle varchar(200), 
		Contact varchar(max), ContactInclude varchar(max), LocationTitle varchar(200), Location varchar(max), LocationInclude varchar(max), 
		CancellationTitle varchar(200), Cancellation varchar(max), CancellationInclude varchar(max), TravelTitle varchar(200), Travel varchar(max), TravelInclude varchar(max), 
		InformationTitle varchar(200), Information varchar(max), RegistrationReplyEmail varchar(400));
	CREATE TABLE #tmpExistingRecurringEvents (eventID int PRIMARY KEY, siteResourceID int, eventCode varchar(15), startDate datetime, endDate datetime);
	CREATE TABLE #tmp_CF_ItemIDs (itemID int, itemType varchar(20));
	CREATE TABLE #tmp_CF_FieldData (fieldID int, fieldValue varchar(max), amount decimal(14,2), itemID int);

	-- Audit logging variables
	DECLARE @orgID int, @calendarID int, @eventTitle varchar(200), @msgjson varchar(max);

	DECLARE @recurringSeriesID int, @swcfList varchar(max), @fullsql varchar(max), @tmpSuffix varchar(36), 
		@eventStartDate datetime, @eventEndDate datetime, @calendarPageName varchar(50), @categoryIDList varchar(200), 
		@categoryList varchar(max), @defaultTimeZoneID int, @eventSiteResourceID int;

	SET @tmpSuffix = REPLACE(CAST(NEWID() AS varchar(36)),'-','');

	SELECT @defaultTimeZoneID = defaultTimeZoneID 
	FROM dbo.sites 
	WHERE siteID = @siteID;
	
	-- Get audit data for recurring events update logging
	SELECT @orgID = s.orgID, @calendarID = ce.calendarID, @eventTitle = eventcontent.contentTitle
	FROM dbo.sites s
	INNER JOIN dbo.ev_events e ON e.siteID = s.siteID
	INNER JOIN dbo.ev_calendarEvents ce ON ce.sourceEventID = e.eventID AND ce.calendarID = ce.sourceCalendarID
	INNER JOIN dbo.cms_contentLanguages AS eventContent ON eventContent.contentID = e.eventContentID 
		AND eventContent.languageID = 1
	WHERE e.eventID = @copyFromEventID;

	SELECT @recurringSeriesID = e.recurringSeriesID, @eventStartDate = et.startTime, @eventEndDate = et.endTime, 
		@calendarPageName = aip.pageName, @categoryIDList = cat.categoryIDList, @eventSiteResourceID = e.siteResourceID
	FROM dbo.ev_events AS e
	INNER JOIN dbo.ev_times AS et ON et.eventid = e.eventID
		AND et.timeID = e.defaultTimeID
	INNER JOIN dbo.ev_calendarEvents AS ce ON ce.sourceEventID = e.eventID 
		AND ce.calendarID = ce.sourceCalendarID
	INNER JOIN dbo.ev_calendars AS c ON c.siteID = @siteID
		AND c.calendarID = ce.calendarID
	INNER JOIN dbo.cms_applicationInstances AS ai ON ai.siteID = @siteID
		AND ai.applicationInstanceID = c.applicationInstanceID
	CROSS APPLY dbo.fn_cms_getApplicationInstancePagePath(@siteID,c.applicationInstanceID) as aip
	LEFT OUTER JOIN dbo.cache_calendarEventsCategoryIDList AS cat ON cat.eventID = e.eventID 
		AND cat.calendarID = ce.calendarID
	WHERE e.eventID = @copyFromEventID
	AND e.siteID = @siteID
	AND aip.applicationSiteResourceID = ai.siteResourceID
	AND aip.applicationSiteResourceType = 'Events';

	-- site doesn't support recurring events or not an recurring event
	IF @recurringSeriesID IS NULL
		GOTO on_done;

	SELECT @categoryList = STRING_AGG(evCat.category,'|')
	FROM dbo.fn_IntListToTable(@categoryIDList,',') as tmpCat
	INNER JOIN dbo.ev_categories as evCat on evCat.categoryID = tmpCat.listItem;

	SET @categoryList = ISNULL(@categoryList,'');

	-- upcoming recurring events
	INSERT INTO #tmpExistingRecurringEvents (eventID, siteResourceID, eventCode, startDate, endDate)
	SELECT e.eventID, e.siteResourceID, e.reportCode, et.startTime, et.endTime
	FROM dbo.ev_events AS e
	INNER JOIN dbo.ev_times AS et ON et.eventid = e.eventID
		AND et.timeID = e.defaultTimeID
	WHERE e.recurringSeriesID = @recurringSeriesID
	AND e.siteID = @siteID
	AND e.[status] IN ('A','I')
	AND et.startTime > @eventStartDate;

	IF @@ROWCOUNT = 0
		GOTO on_done;
	
	-- event custom fields	
	IF OBJECT_ID('tempdb..##tmpSWCF'+@tmpSuffix) IS NOT NULL
		EXEC ('DROP TABLE ##tmpSWCF'+@tmpSuffix);
	
	INSERT INTO #tmp_CF_ItemIDs (itemID, itemType)
	SELECT siteResourceID, 'CrossEvent'
	FROM dbo.ev_events
	WHERE eventID = @copyFromEventID
	AND siteID = @siteID;

	EXEC dbo.cf_getFieldData;

	SELECT e.eventID, replace(f.fieldReference,',','') as fieldReference, 
		CASE WHEN ft.displayTypeCode IN ('SELECT','RADIO','CHECKBOX') THEN REPLACE(fd.fieldValue,', ', '|') ELSE fd.fieldValue END AS answer
	INTO #tmpSWCF
	FROM #tmp_CF_FieldData AS fd
	INNER JOIN dbo.cf_fields AS f ON f.fieldID = fd.fieldID
	INNER JOIN dbo.cf_fieldTypes AS ft ON ft.fieldTypeID = f.fieldTypeID
	INNER JOIN dbo.ev_events as e ON e.siteID = @siteID and e.siteResourceID = fd.itemID;

	-- event custom fields pivoted
	set @swcfList = '';
	select @swcfList = COALESCE(@swcfList + ',', '') + quoteName(fieldReference) from #tmpSWCF group by fieldReference;
	IF left(@swcfList,1) = ','
		set @swcfList = right(@swcfList,len(@swcfList)-1);
	IF len(@swcfList) > 0 BEGIN
		-- add swcf cols to import table
		select @fullsql = COALESCE(@fullsql, '') + 'ALTER TABLE #mc_EvImport ADD ' + quoteName(fieldReference)  + ' varchar(max);'
		from #tmpSWCF 
		group by fieldReference;

		EXEC(@fullsql);

		set @fullsql = '
			select * 
			into ##tmpSWCF'+@tmpSuffix+'
			from (
				select eventID, fieldReference, answer
				from #tmpSWCF
			) as cf
			PIVOT (min(answer) for fieldReference in (' + @swcfList + ')) as p ';
		EXEC(@fullsql);
	END
	ELSE
		EXEC('SELECT eventID INTO ##tmpSWCF'+@tmpSuffix+' FROM #tmpSWCF WHERE 0=1');

	-- prep final data for import
	SET @fullsql = 'SELECT DISTINCT ROW_NUMBER() OVER (ORDER BY tmp.startDate) AS rowID, '''+@calendarPageName+''', eventcontent.contentTitle, ev.eventSubTitle, ev.internalNotes, tmp.eventCode, '''+@categoryList+''', 
		tmp.startDate, tmp.endDate, ISNULL(ev.hiddenFromCalendar,0), ev.isAllDayEvent, eventcontent.rawContent, contactcontent.contentTitle, contactcontent.rawContent, 
		ISNULL(ev.emailContactContent,0), locationcontent.contentTitle, locationcontent.rawContent, ISNULL(ev.emailLocationContent,0), cancelcontent.contentTitle, cancelcontent.rawContent, 
		ISNULL(ev.emailCancelContent,0), travelcontent.contentTitle, travelcontent.rawContent, ISNULL(ev.emailTravelContent,0), 
		informationcontent.contentTitle, informationcontent.rawContent, '''' AS RegistrationReplyEmail';
	IF len(@swcfList) > 0
		SET @fullsql = @fullsql + ', swcf.' + replace(@swcfList,',',',swcf.');
	SET @fullsql = @fullsql + '
		FROM #tmpExistingRecurringEvents AS tmp
		INNER JOIN dbo.ev_events AS ev ON ev.siteID = '+CAST(@siteID AS varchar(10))+' AND ev.eventID = '+CAST(@copyFromEventID AS varchar(10))+'
		CROSS APPLY dbo.fn_getContent(ev.eventcontentID,1) AS eventcontent
		CROSS APPLY dbo.fn_getContent(ev.locationcontentID,1) AS locationcontent
		CROSS APPLY dbo.fn_getContent(ev.travelcontentID,1) AS travelcontent
		CROSS APPLY dbo.fn_getContent(ev.contactcontentID,1) AS contactcontent
		CROSS APPLY dbo.fn_getContent(ev.cancellationPolicycontentID,1) AS cancelcontent
		CROSS APPLY dbo.fn_getContent(ev.informationContentID,1) AS informationcontent';
	IF len(@swcfList) > 0
		SET @fullsql = @fullsql + '
			LEFT OUTER JOIN ##tmpSWCF'+@tmpSuffix+' AS swcf ON swcf.eventID = ev.eventID';

	INSERT INTO #mc_EvImport
	EXEC(@fullsql);

	-- drop global table
	IF OBJECT_ID('tempdb..##tmpSWCF'+@tmpSuffix) IS NOT NULL
		EXEC ('DROP TABLE ##tmpSWCF'+@tmpSuffix);

	-- Audit logging before updating recurring events
	IF @calendarID IS NOT NULL AND @orgID IS NOT NULL BEGIN
		DECLARE @eventCount int, @evKeyMapJSON varchar(100);
		SELECT @eventCount = COUNT(*) FROM #tmpExistingRecurringEvents;

		SET @evKeyMapJSON = '{ "EVENTID":'+CAST(@copyFromEventID AS varchar(10))+', "CALENDARID":'+CAST(@calendarID AS VARCHAR(10))+' }';
		SET @msgjson = STRING_ESCAPE('Upcoming recurring events updated for event [' + @eventTitle + '], updating [' + CAST(@eventCount AS varchar(10)) + '] future events in the series.', 'json');

		EXEC dbo.ev_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='EVENT', @msgjson=@msgjson, @evKeyMapJSON=@evKeyMapJSON, @isImport=0, @enteredByMemberID=@recordedByMemberID;
	END

	-- queue recurring events import
	EXEC dbo.ev_importEvents @siteID=@siteID, @recordedByMemberID=@recordedByMemberID, @ovAction='o', @importResult=@recurringEventsImportResult OUTPUT;

	IF @recurringEventsImportResult.value('count(/import/errors/error)','int') = 0 BEGIN
		-- update asset categories
		DELETE csr
		FROM dbo.cms_categorySiteResources AS csr
		INNER JOIN #tmpExistingRecurringEvents AS tmp ON tmp.siteResourceID = csr.siteResourceID;

		INSERT INTO dbo.cms_categorySiteResources (categoryID, siteResourceID)
		SELECT DISTINCT csr.categoryID, tmp.siteResourceID
		FROM #tmpExistingRecurringEvents AS tmp
		INNER JOIN dbo.cms_categorySiteResources AS csr ON csr.siteResourceID = @eventSiteResourceID;
	END

	on_done:

	IF OBJECT_ID('tempdb..#mc_EvImport') IS NOT NULL
		DROP TABLE #mc_EvImport;
	IF OBJECT_ID('tempdb..#tmpExistingRecurringEvents') IS NOT NULL
		DROP TABLE #tmpExistingRecurringEvents;
	IF OBJECT_ID('tempdb..#tmp_CF_ItemIDs') IS NOT NULL
		DROP TABLE #tmp_CF_ItemIDs;
	IF OBJECT_ID('tempdb..#tmp_CF_FieldData') IS NOT NULL
		DROP TABLE #tmp_CF_FieldData;
	IF OBJECT_ID('tempdb..#tmpSWCF') IS NOT NULL
		DROP TABLE #tmpSWCF;
	IF OBJECT_ID('tempdb..##tmpSWCF'+@tmpSuffix) IS NOT NULL
		EXEC ('DROP TABLE ##tmpSWCF'+@tmpSuffix);

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
