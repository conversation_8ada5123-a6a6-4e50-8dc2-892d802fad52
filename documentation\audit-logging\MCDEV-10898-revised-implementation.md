# MCDEV-10898: Revised Implementation - Events Registration Tab Audit Logging

## Changes Made Based on User Feedback

### 1. Modified saveEventRegDetails() Method Approach

**Previous Implementation**: Used temp table pattern for both new registration creation and updates
**Revised Implementation**: 
- **New Registration Creation** (`registrationid = 0`): Uses **direct logging** instead of temp table
- **Existing Registration Updates** (`registrationid > 0`): Uses **temp table pattern** for field change tracking
- **Content Updates**: Added **separate audit logging** for `cms_updateContent` calls

### 2. Detailed Changes

#### A. New Registration Creation (Direct Logging)
**Location**: `membercentral/model/admin/events/EventAdmin.cfc` (lines 3617-3657)

```sql
-- Direct audit logging for new registration creation
SELECT @registrationTypeName = CASE @registrationTypeID WHEN 1 THEN 'Full Registration' WHEN 2 THEN 'RSVP' ELSE 'Unknown' END;
SET @evKeyMapJSON = '{ "REGISTRATIONID":' + CAST(@registrationID AS varchar(10)) + ', "EVENTID":' + CAST(@eventID AS varchar(10)) + ' }';
SET @msgjson = STRING_ESCAPE('New registration created for event [' + ISNULL(@eventTitle, 'Unknown Event') + '] with type [' + @registrationTypeName + '] from ' + CONVERT(varchar(20), @startDate, 120) + ' to ' + CONVERT(varchar(20), @endDate, 120) + 
    CASE WHEN @registrantCap IS NOT NULL THEN ' with capacity limit of ' + CAST(@registrantCap AS varchar(10)) ELSE '' END + '.', 'json');
EXEC dbo.ev_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='REGISTRATION', @msgjson=@msgjson, @evKeyMapJSON=@evKeyMapJSON, @isImport=0, @enteredByMemberID=@enteredByMemberID;
```

**Features**:
- Single audit entry with comprehensive registration details
- Includes registration type, date range, and capacity information
- No temp table overhead for new registrations

#### B. Existing Registration Updates (Temp Table Pattern)
**Location**: `membercentral/model/admin/events/EventAdmin.cfc` (lines 3659-3683)

```sql
-- Updating existing registration - create temp audit table for tracking changes
IF OBJECT_ID('tempdb..##tmpRegAuditLogData') IS NOT NULL
    DROP TABLE ##tmpRegAuditLogData;
CREATE TABLE ##tmpRegAuditLogData([rowCode] varchar(20) PRIMARY KEY, 
    [Registration Type] varchar(max), [Start Date] varchar(max), [End Date] varchar(max), 
    [Registrant Cap] varchar(max), [Reply To Email] varchar(max), [Notify Email] varchar(max),
    [Online Meeting] varchar(max), [Online Embed Code] varchar(max), [Online Override Link] varchar(max),
    [Online Start Time] varchar(max), [Online End Time] varchar(max), [Real Time Roster] varchar(max),
    [Edit Allowed] varchar(max), [Edit Deadline] varchar(max));
```

**Features**:
- Temp table created only for updates, not new registrations
- Tracks 15 registration fields for comprehensive change detection
- Uses `ams_getAuditLogMsg` for intelligent change detection

#### C. Content Updates Audit Logging
**Location**: `membercentral/model/admin/events/EventAdmin.cfc` (lines 3791-3801)

```sql
-- Audit logging for content updates
DECLARE @contentUpdateMsg varchar(max) = '';
IF LEN(@expireContent) > 0 OR LEN(@registrantCapContent) > 0 OR LEN(@regEditDeadlineContent) > 0 OR LEN(@regEditRefundContent) > 0 BEGIN
    SET @contentUpdateMsg = 'Registration content updated: ';
    IF LEN(@expireContent) > 0 SET @contentUpdateMsg = @contentUpdateMsg + 'Expiration Message, ';
    IF LEN(@registrantCapContent) > 0 SET @contentUpdateMsg = @contentUpdateMsg + 'Registrant Cap Message, ';
    IF LEN(@regEditDeadlineContent) > 0 SET @contentUpdateMsg = @contentUpdateMsg + 'Editing Expiration Message, ';
    IF LEN(@regEditRefundContent) > 0 SET @contentUpdateMsg = @contentUpdateMsg + 'Editing Refund Policy, ';
    -- Remove trailing comma and space
    SET @contentUpdateMsg = LEFT(@contentUpdateMsg, LEN(@contentUpdateMsg) - 1);
    SET @contentUpdateMsg = STRING_ESCAPE(@contentUpdateMsg + ' for event [' + ISNULL(@eventTitle, 'Unknown Event') + '].', 'json');
    EXEC dbo.ev_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='REGISTRATION', @msgjson=@contentUpdateMsg, @evKeyMapJSON=@evKeyMapJSON, @isImport=0, @enteredByMemberID=@enteredByMemberID;
END
```

**Features**:
- Tracks all four content types: Expiration Message, Registrant Cap Message, Editing Expiration Message, Editing Refund Policy
- Only logs when content is actually provided (LEN > 0)
- Works for both new registrations and updates
- Separate audit entry from field changes

### 3. Audit Logging Flow

#### For New Registration Creation:
1. **Registration Creation**: Direct audit log with registration details
2. **Content Updates**: Separate audit log for any content provided

#### For Existing Registration Updates:
1. **Field Changes**: Temp table pattern with change detection
2. **Content Updates**: Separate audit log for any content changes

### 4. Benefits of Revised Approach

#### Performance Improvements:
- **New Registrations**: No temp table overhead, single direct audit entry
- **Updates**: Temp table only created when needed for change tracking

#### Audit Clarity:
- **New Registrations**: Clear "creation" message with all initial settings
- **Updates**: Detailed field-by-field change tracking
- **Content**: Separate tracking of content updates regardless of registration type

#### Maintainability:
- **Cleaner Logic**: Separate paths for creation vs updates
- **Focused Tracking**: Content updates tracked independently
- **Consistent Format**: All entries use same evKeyMapJSON format

### 5. evKeyMapJSON Format (Unchanged)
All audit entries continue to use the specified format:
```json
{ "REGISTRATIONID":123, "EVENTID":456 }
```

### 6. Area Code (Unchanged)
All audit entries use `'REGISTRATION'` as the area code.

### 7. Testing Considerations

#### New Registration Testing:
- Verify single audit entry for registration creation
- Verify separate audit entry for content updates
- Confirm no temp table creation overhead

#### Update Registration Testing:
- Verify temp table pattern for field changes
- Verify separate audit entry for content updates
- Confirm proper change detection and cleanup

#### Content Update Testing:
- Test with various combinations of content fields
- Verify content-only updates are properly logged
- Confirm content logging works for both new and existing registrations

## Summary

The revised implementation provides:
1. **Optimized Performance**: Direct logging for new registrations eliminates unnecessary temp table overhead
2. **Comprehensive Tracking**: Separate audit entries for registration fields and content updates
3. **Clear Audit Trail**: Distinct messages for creation vs updates vs content changes
4. **Maintained Consistency**: Same evKeyMapJSON format and area code across all entries

This approach better aligns with the user's requirements while maintaining the robust audit logging capabilities established in previous stories.
