ALTER PROCEDURE dbo.ev_createRegistration
@eventID int,
@registrationTypeID int,
@startDate datetime,
@endDate datetime,
@registrantCap int,
@replyToEmail varchar(200),
@notifyEmail varchar(200),
@isPriceBasedOnActual bit,
@bulkCountByRate bit,
@registrationID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	BEGIN TRANSACTION;

	declare @appCreatedContentResourceTypeID int, @defaultLanguageID int, @enableRealTimeRoster bit,
			@contentSiteResourceID int, @siteid int, @expirationcontentid int, @paymentContentId int, @registrantCapContentID int,
			@registrantEditDeadlineContentID int, @registrantEditRefundContentID int;

	-- Audit logging variables
	DECLARE @orgID int, @calendarID int, @eventTitle varchar(200), @registrationTypeName varchar(100), @msgjson varchar(max);

	set @registrationID = null;
	select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent');

	-- Get site info and audit data
	select @siteID=s.siteID, @defaultLanguageID=s.defaultLanguageID, @orgID=s.orgID
	from dbo.sites as s
	inner join dbo.ev_events as e on e.siteID = s.siteID
	where e.eventid = @eventID;

	-- Get calendar info for eventKeyMapJSON pattern
	SELECT @calendarID = ce.calendarID
	FROM dbo.ev_events e
	INNER JOIN dbo.ev_calendarEvents ce ON ce.sourceEventID = e.eventID AND ce.calendarID = ce.sourceCalendarID
	WHERE e.eventID = @eventID;

	-- Get event title for audit message	
	SELECT TOP 1 @eventTitle = eventcontent.contentTitle
	FROM dbo.ev_events as e 
	INNER JOIN dbo.cms_contentLanguages AS eventContent ON eventContent.contentID = e.eventContentID 
		AND eventContent.languageID = 1
	WHERE e.eventID = @eventID;

	-- Get registration type name for audit message
	SELECT @registrationTypeName = registrationType
	FROM dbo.ev_registrationTypes
	WHERE registrationTypeID = @registrationTypeID;

	select top 1 @enableRealTimeRoster = c.enableRealTimeRoster
	from dbo.ev_events as ev
	INNER JOIN dbo.ev_calendarEvents as ce on ce.sourceEventID = ev.eventID and ce.calendarID = ce.sourceCalendarID
	INNER JOIN dbo.ev_calendars as c on c.siteID = @siteID and c.calendarID = ce.calendarID
	where ev.siteID = @siteID
	and ev.eventID = @eventID;

	SELECT @registrationID = registrationID
	FROM dbo.ev_registration WITH (UPDLOCK, HOLDLOCK)
	WHERE eventID = @eventID
	AND [status] <> 'D';

	IF @registrationID IS NULL BEGIN
		-- add entry without content objects to get the entry in immediately
		insert into dbo.ev_registration (eventID, siteID, registrationTypeID, startDate, endDate, registrantCap,  
			replyToEmail, notifyEmail, [status], isPriceBasedOnActual, bulkCountByRate, enableRealTimeRoster)
		values (@eventID, @siteID, @registrationTypeID, @startDate, @endDate, @registrantCap,  
			@replyToEmail, @notifyEmail, 'A', @isPriceBasedOnActual, @bulkCountByRate, @enableRealTimeRoster);

		select @registrationID = SCOPE_IDENTITY()

		-- create default reg schedule entry
		IF @registrationTypeID = 1 
			insert into dbo.ev_priceSchedule (registrationID, rangeName, startDate, endDate)
			values (@registrationID, 'Regular Registration', @startDate, @endDate);

		-- now create the content objects and update registration
		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
			@siteResourceStatusID=1, @isHTML=1, @languageID=@defaultLanguageID, 
			@isActive=1, @contentTitle='Expiration Message', @contentDesc=null, @rawContent='', @memberID=NULL, 
			@contentID=@expirationcontentid OUTPUT, @siteResourceID=@contentSiteResourceID OUTPUT;

		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
			@siteResourceStatusID=1, @isHTML=1, @languageID=@defaultLanguageID, 
			@isActive=1, @contentTitle='Registration Cap Message', @contentDesc=null, @rawContent='', @memberID=NULL,
			@contentID=@registrantCapContentID OUTPUT, @siteResourceID=@contentSiteResourceID OUTPUT;

		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
			@siteResourceStatusID=1, @isHTML=1, @languageID=@defaultLanguageID, 
			@isActive=1, @contentTitle='Editing Expiration Message', @contentDesc=null, @rawContent='', @memberID=NULL, 
			@contentID=@registrantEditDeadlineContentID OUTPUT, @siteResourceID=@contentSiteResourceID OUTPUT;

		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
			@siteResourceStatusID=1, @isHTML=1, @languageID=@defaultLanguageID, 
			@isActive=1, @contentTitle='Editing Refund Policy', @contentDesc=null, @rawContent='', @memberID=NULL, 
			@contentID=@registrantEditRefundContentID OUTPUT, @siteResourceID=@contentSiteResourceID OUTPUT;

		UPDATE dbo.ev_registration
		SET expirationContentID = @expirationcontentid,
			registrantCapContentID = @registrantCapContentID,
			registrantEditDeadlineContentID = @registrantEditDeadlineContentID,
			registrantEditRefundContentID = @registrantEditRefundContentID
		WHERE registrationID = @registrationID;

		-- Audit logging for registration creation
		IF @calendarID IS NOT NULL AND @orgID IS NOT NULL BEGIN
			DECLARE @evKeyMapJSON varchar(100) = '{ "EVENTID":'+CAST(@eventID AS varchar(10))+', "CALENDARID":'+CAST(@calendarID AS VARCHAR(10))+', "REGISTRATIONID":'+CAST(@registrationID AS VARCHAR(10))+' }';
			SET @msgjson = STRING_ESCAPE('Registration setup created for event [' + @eventTitle + '] with type [' + ISNULL(@registrationTypeName, 'Unknown') + '] from [' + CONVERT(varchar(20), @startDate, 120) + '] to [' + CONVERT(varchar(20), @endDate, 120) + '].', 'json');

			-- Note: Using @enteredByMemberID=0 since this procedure doesn't have that parameter
			-- In practice, this will be called from contexts where the member ID is known
			EXEC dbo.ev_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='REGISTRATION', @msgjson=@msgjson, @evKeyMapJSON=@evKeyMapJSON, @isImport=0, @enteredByMemberID=0;
		END
	END

	COMMIT TRANSACTION;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
